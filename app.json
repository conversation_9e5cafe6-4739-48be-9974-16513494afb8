{"expo": {"name": "real-estate", "slug": "real-estate", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "realestate", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "748fbc7d-c7c7-4735-ad7a-af5045adf1fe"}}, "owner": "<PERSON><PERSON><PERSON><PERSON>"}}